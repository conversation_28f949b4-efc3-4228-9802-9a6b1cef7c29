/* 主题切换器样式 */
.switcher-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  padding: 20rpx;
  box-sizing: border-box;
}

.switcher-overlay.show {
  opacity: 1;
  visibility: visible;
}

.switcher-content {
  background-color: #ffffff;
  border-radius: 32rpx;
  width: 90%;
  max-width: 640rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.switcher-overlay.show .switcher-content {
  transform: scale(1);
}

.switcher-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.switcher-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.switcher-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 32rpx;
  color: #999999;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.switcher-close:active {
  background: #f0f0f0;
}

.dashboard-list {
  padding: 20rpx 0;
  max-height: 600rpx;
  overflow-y: auto;
}

.dashboard-option {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  transition: all 0.3s ease;
  position: relative;
  border: 2rpx solid #e6e9ef;
  border-radius: 32rpx;
  margin: 16rpx 24rpx;
  background: #ffffff;
}

.dashboard-option:active {
  background: #f5f8ff;
}

.dashboard-option.active {
  background: #e6f0ff;
  border-color: #2a6df5;
  box-shadow: 0 0 0 3rpx rgba(42, 109, 245, 0.08);
}

.dashboard-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.dashboard-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.dashboard-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.dashboard-desc {
  font-size: 26rpx;
  color: #666666;
}

/* 移除“当前”badge 样式（不再使用） */

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
