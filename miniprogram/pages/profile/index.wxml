<!-- 个人页面 -->
<view class="profile-container">
  <!-- 用户信息区域 -->
  <view class="section" bindtap="onEditUserInfo">
    <view class="user-header">
      <view class="user-avatar">
        <image wx:if="{{userInfo.avatar}}" src="{{userInfo.avatar}}" class="avatar-image" />
        <view wx:else class="avatar-placeholder">{{userInfo.nickname.charAt(0)}}</view>
      </view>

      <view class="user-details">
        <view class="user-nickname">{{userInfo.nickname || '未设置昵称'}}</view>
        <view class="user-subtitle">ID: {{userInfo.no || '?'}}</view>
      </view>

      <!-- 签到按钮 -->
      <view class="check-in-btn-container">
        <view wx:if="{{checkInStatus.loading}}">
          <view class="check-in-btn loading">
            <text class="btn-text">加载中...</text>
          </view>
        </view>
        <view wx:elif="{{!checkInStatus.hasCheckedInToday}}" catchtap="onQuickCheckIn">
          <view class="check-in-btn">
            <text class="btn-text">今日未签到</text>
          </view>
        </view>
        <view wx:else catchtap="onGoToCheckIn">
          <view class="check-in-btn checked">
            <text class="btn-text">已连续签到 {{checkInStatus.consecutiveDays}} 天</text>
            <view class="setting-arrow">›</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 用户统计区域 -->
  <view class="section">
    <view class="stats-grid">
      <view class="stat-item" bindtap="onShowRegistrationDays">
        <view class="stat-label">已加入</view>
        <view class="stat-value">{{userStats.registrationDays || 0}}</view>
        <view class="stat-label">天</view>
      </view>
      <view class="stat-item" bindtap="onViewRedemptionCodes">
        <view class="stat-label">兑换码</view>
        <view class="stat-value">{{userStats.redemptionCodes || 0}}</view>
        <view class="setting-arrow">›</view>
      </view>
      <view class="stat-item" bindtap="onViewPoints">
        <view class="stat-label">积分</view>
        <view class="stat-value">{{userInfo.points || 0}}</view>
        <view class="setting-arrow">›</view>
      </view>
    </view>
  </view>

  <!-- 会员信息区域 -->
  <view class="section">
    <view class="membership-card {{userInfo.vip.status ? 'vip' : ''}}" bindtap="onGoToMembershipPage">
      <view class="membership-content">
        <view class="membership-left">
          <text class="crown-icon">👑</text>
          <view class="membership-info">
            <text class="membership-title">{{userInfo.vip.status ? 'VIP会员' : '免费用户'}}</text>
            <text wx:if="{{userInfo.vip.status}}" class="membership-subtitle">{{vipExpireText}}</text>
            <text wx:else class="membership-subtitle">升级VIP享受更多权益</text>
          </view>
        </view>

        <view class="membership-right">
          <view class="setting-arrow">›</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 会员功能区域 -->
  <view class="settings-section">
    <view class="settings-list">
      <view class="setting-item" bindtap="onViewMembershipBenefits">
        <view class="setting-left">
          <view class="setting-icon">💎</view>
          <text class="setting-title">会员权益</text>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <view class="setting-item" bindtap="onGoToStore">
        <view class="setting-left">
          <view class="setting-icon">🛒</view>
          <text class="setting-title">积分商店</text>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <view class="setting-item" bindtap="onShowRedeemModal">
        <view class="setting-left">
          <view class="setting-icon">🎫</view>
          <text class="setting-title">使用兑换码</text>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <view wx:if="{{!userInfo.vip.status}}" class="setting-item upgrade-item" bindtap="onUpgradeMembership">
        <view class="setting-left">
          <view class="setting-icon">⭐</view>
          <text class="setting-title">获取VIP会员</text>
        </view>
        <view class="setting-arrow">›</view>
      </view>
    </view>
  </view>

  <!-- 其他设置 -->
  <view class="settings-section">
    <view class="settings-list">
      <!-- 数据管理 -->
      <view class="setting-item" bindtap="onGoToDataManagement">
        <view class="setting-left">
          <view class="setting-icon">📊</view>
          <text class="setting-title">数据管理</text>
        </view>
        <view class="setting-arrow">›</view>
      </view>
      <!-- 显示货币 -->
      <view class="setting-item">
        <view class="setting-left">
          <view class="setting-icon">💱</view>
          <text class="setting-title">显示货币</text>
        </view>
        <view class="setting-right">
          <picker mode="selector" range="{{currencyOptions}}" range-key="label" value="{{selectedCurrencyIndex}}" bindchange="onCurrencyChange">
            <view class="picker-value">{{currencyOptions[selectedCurrencyIndex].label}}</view>
          </picker>
        </view>
      </view>
    </view>
  </view>

  <!-- 应用信息 -->
  <view class="settings-section">
    <view class="settings-list">
      <!-- 友情应用 -->
      <view class="setting-item" bindtap="onGoToFriendApps">
        <view class="setting-left">
          <view class="setting-icon">📱</view>
          <text class="setting-title">友情应用</text>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <!-- 加入摸鱼社群 -->
      <view class="setting-item" bindtap="onJoinCommunity">
        <view class="setting-left">
          <view class="setting-icon">🐟</view>
          <text class="setting-title">加入摸鱼社群</text>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <!--   意见反馈 -->
      <view class="setting-item" bindtap="onGoToFeedback">
        <view class="setting-left">
          <view class="setting-icon">💬</view>
          <text class="setting-title">意见反馈</text>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <!-- 公告中心 -->
      <view class="setting-item" bindtap="onViewAnnouncements">
        <view class="setting-left">
          <view class="setting-icon">📢</view>
          <text class="setting-title">公告中心</text>
        </view>
        <view class="setting-arrow">›</view>
      </view>
    </view>
  </view>

  <!-- 页面底部信息 -->
  <view class="app-info-footer">
    <view class="app-name">{{appInfo.name}}</view>
    <view class="app-desc">{{appInfo.description}}</view>
    <view class="app-version">v{{versionInfo.version}} ({{versionInfo.date}})</view>
  </view>

  <!-- 用户信息编辑模态框 -->
  <view class="user-edit-modal {{showUserEditModal ? 'show' : ''}}" bindtap="onCloseUserEditModal">
    <view class="modal-content" catchtap="onModalContentTap">
      <view class="modal-header">
        <text class="modal-title">编辑个人信息</text>
        <view class="modal-close" bindtap="onCloseUserEditModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 头像编辑 -->
        <view class="section">
          <button class="modal-avatar" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
            <image wx:if="{{tempUserInfo.avatar}}" src="{{tempUserInfo.avatar}}" class="modal-avatar-image" />
            <view wx:else class="modal-avatar-placeholder">{{tempUserInfo.nickname.charAt(0)}}</view>
            <view class="modal-avatar-overlay">
              <text class="modal-avatar-text">点击更换头像</text>
            </view>
          </button>
        </view>

        <!-- 昵称编辑 -->
        <view class="section">
          <input
            type="nickname"
            class="modal-nickname-input"
            placeholder="请输入昵称"
            value="{{tempUserInfo.nickname}}"
            bindinput="onTempNicknameInput"
            maxlength="20"
            focus="{{focusNickname}}"
          />
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="onCloseUserEditModal">取消</button>
        <button class="modal-btn confirm" bindtap="onSaveUserInfo">保存</button>
      </view>
    </view>
  </view>

  <!-- VIP记录模态框 -->
  <view class="vip-records-modal {{showVipRecordsModal ? 'show' : ''}}" bindtap="onCloseVipRecordsModal">
    <view class="modal-content" catchtap="onModalContentTap">
      <view class="modal-header">
        <text class="modal-title">VIP获取记录</text>
        <view class="modal-close" bindtap="onCloseVipRecordsModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 统计信息 -->
        <view wx:if="{{vipRecordsStats}}" class="records-stats">
          <view class="stats-item">
            <text class="stats-label">总获取天数</text>
            <text class="stats-value">{{vipRecordsStats.totalDays}}天</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">获取次数</text>
            <text class="stats-value">{{vipRecordsStats.totalRecords}}次</text>
          </view>
        </view>

        <!-- 记录列表 -->
        <view class="records-list">
          <view wx:if="{{vipRecords.length === 0}}" class="empty-records">
            <text class="empty-text">暂无VIP获取记录</text>
          </view>

          <view wx:for="{{vipRecords}}" wx:key="_id" class="record-item">
            <view class="record-icon">
              <text class="icon-text">{{item.type === 'new_user' ? '🎁' : item.type === 'activation_code' ? '🔑' : item.type === 'watch_ad' ? '📺' : item.type === 'invite_friend' ? '👥' : '💎'}}</text>
            </view>

            <view class="record-content">
              <view class="record-title">{{item.description}}</view>
              <view class="record-details">
                <text class="record-date">{{item.createTimeText}}</text>
                <text class="record-days">+{{item.days}}天</text>
              </view>
              <view wx:if="{{item.source && item.source !== 'system' && item.source !== 'ad_reward'}}" class="record-source">
                <text class="source-text">来源：{{item.source}}</text>
              </view>
            </view>

            <view class="record-status">
              <text class="status-text {{item.status === 'active' ? 'active' : 'expired'}}">
                {{item.status === 'active' ? '有效' : '已过期'}}
              </text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view wx:if="{{hasMoreVipRecords}}" class="load-more" bindtap="onLoadMoreVipRecords">
          <text class="load-more-text">加载更多</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 兑换码使用模态框 -->
  <redeem-code-modal
    show="{{showRedeemModal}}"
    default-code="{{defaultRedeemCode}}"
    bind:success="onRedeemSuccess"
    bind:close="onCloseRedeemModal"
  />

  <!-- 摸鱼社群模态框 -->
  <community-modal
    show="{{showCommunityModal}}"
    bind:close="onCloseCommunityModal"
  />
</view>