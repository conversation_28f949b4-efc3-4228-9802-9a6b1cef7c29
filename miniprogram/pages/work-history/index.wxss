page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 工作履历页面样式 */
.work-history-page {
  min-height: 100vh;
  background-color: #F5F7FB;
  padding: 20rpx;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.header-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.header-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.header-actions {
  margin-left: 20rpx;
}

.add-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(6, 182, 212, 0.3);
}

.add-icon {
  margin-right: 8rpx;
  font-size: 32rpx;
  font-weight: 300;
}

/* 工作履历列表 */
.work-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #1a1a1a;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 现代化工作履历卡片 */
.modern-work-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
}

.modern-work-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border-color: #e0e0e0;
}

.modern-work-card.current-work {
  border-left: 4rpx solid #06b6d4;
  background: linear-gradient(135deg, #fafefe 0%, #f5fffb 100%);
}

.modern-work-card.current-work:hover {
  box-shadow: 0 8rpx 32rpx rgba(6, 182, 212, 0.12);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24rpx;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.card-header:active {
  background-color: #fafafa;
}

.header-left {
  flex: 1;
  min-width: 0;
}

/* 公司信息区域 */
.company-section {
  margin-bottom: 16rpx;
}

.company-name-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 6rpx;
}

.company-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.3;
}

.current-indicator {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 4rpx 12rpx;
  background: #06b6d4;
  border-radius: 12rpx;
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(6, 182, 212, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4rpx rgba(6, 182, 212, 0.1);
  }
}

.current-dot {
  width: 8rpx;
  height: 8rpx;
  background: #ffffff;
  border-radius: 50%;
  animation: pulse-dot 1.5s infinite;
}

@keyframes pulse-dot {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.current-label {
  font-size: 20rpx;
  font-weight: 500;
  color: #ffffff;
}

.position-title {
  font-size: 26rpx;
  color: #6b7280;
  font-weight: 500;
  line-height: 1.4;
}

/* 快速信息标签 */
.quick-info {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.info-chip {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  border-radius: 20rpx;
  transition: all 0.2s ease;
}

.info-chip:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.chip-icon {
  font-size: 16rpx;
  line-height: 1;
}

.chip-text {
  font-size: 22rpx;
  color: #475569;
  font-weight: 500;
  line-height: 1;
}

.salary-chip {
  background: #fef3e2;
  border-color: #fed7aa;
}

.salary-chip .chip-text {
  color: #ea580c;
}

.status-chip {
  position: relative;
}

.status-chip.status-active {
  background: #f0fdf4;
  border-color: #bbf7d0;
}

.status-chip.status-active .chip-text {
  color: #16a34a;
}

.status-chip.status-inactive {
  background: #fef2f2;
  border-color: #fecaca;
}

.status-chip.status-inactive .chip-text {
  color: #dc2626;
}

.status-indicator {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  margin-right: 4rpx;
}

.status-chip.status-active .status-indicator {
  background: #16a34a;
  animation: pulse-green 2s infinite;
}

.status-chip.status-inactive .status-indicator {
  background: #dc2626;
}

@keyframes pulse-green {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 现代化展开按钮 */
.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.expand-button:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: scale(1.05);
}

.expand-button.expanded {
  background: #06b6d4;
  border-color: #06b6d4;
}

.expand-button.expanded:hover {
  background: #0ea5b3;
  transform: scale(1.05);
}

/* 使用Unicode字符的展开图标 */
.expand-icon-text {
  font-size: 18rpx;
  color: #64748b;
  font-weight: normal;
  line-height: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

/* 展开时旋转90度：▶ 变成 ▼ */
.expand-button.expanded .expand-icon-text {
  transform: rotate(90deg);
  color: #ffffff;
}

/* 详情面板 */
.details-panel {
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  background: #ffffff;
  border-top: 1rpx solid #f0f0f0;
}

.details-panel.hide {
  max-height: 0;
  opacity: 0;
}

.details-panel.show {
  opacity: 1;
}

.panel-content {
  padding: 20rpx 24rpx;
}

/* 详情行样式 */
.detail-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 8rpx 0;
}

.detail-row:last-child {
  border-bottom: none;
}

.row-icon {
  width: 32rpx;
  height: 32rpx;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  flex-shrink: 0;
}

.row-text {
  font-size: 24rpx;
  color: #50617a;
  font-weight: 500;
  flex: 1;
}

/* 备注区域 */
.notes-section {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.notes-title {
  font-size: 26rpx;
  color: #6b7280;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.notes-content {
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.6;
  padding: 12rpx 16rpx;
  background: #f9fafb;
  border-radius: 8rpx;
  border-left: 3rpx solid #d1d5db;
  white-space: pre-wrap;
  word-wrap: break-word;
  display: block;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
  margin-top: 24rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 操作按钮样式 */
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  padding: 12rpx 20rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  flex: 1;
  min-width: 100rpx;
  transition: all 0.2s ease;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.action-btn:active {
  transform: scale(0.98);
}

.btn-icon {
  font-size: 16rpx;
  line-height: 1;
}

.btn-text {
  font-size: 24rpx;
  line-height: 1;
}

/* 主要按钮 */
.primary-action {
  background: linear-gradient(135deg, #06b6d4, #10b981);
  color: #ffffff;
}

.primary-action:active {
  background: #0ea5b3;
}

/* 次要按钮 */
.secondary-action {
  background: #ffffff;
  color: #06b6d4;
  border: 1rpx solid #e0e7ff;
}

.secondary-action:active {
  background: #f8faff;
}

/* 危险按钮 */
.danger-action {
  background: #ffffff;
  color: #dc2626;
  border: 1rpx solid #fecaca;
}

.danger-action:active {
  background: #fef2f2;
}
