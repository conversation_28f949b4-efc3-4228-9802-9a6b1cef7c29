/**
 * 数据管理页面
 * 提供本地和云端数据管理功能
 */

const { api } = require('../../core/api/index.js')
const {
  calculateUserDataStats,
  formatStatsText,
  formatDateTime,
  formatHistoryDate,
  formatHistoryTime
} = require('../../utils/data-stats.js')

Page({
  data: {
    // 本地数据统计
    localStats: {
      workHistoryCount: 0,
      timeSegmentCount: 0,
      fishingRecordCount: 0,
      incomeAdjustmentCount: 0,
      lastModified: null,
      lastModifiedText: '未知'
    },

    // 云端数据统计
    cloudStats: {
      workHistoryCount: 0,
      timeSegmentCount: 0,
      fishingRecordCount: 0,
      incomeAdjustmentCount: 0,
      lastModified: null,
      lastModifiedText: '未知'
    },

    // 上传按钮状态
    canUpload: false,
    uploadButtonText: '检查中...',

    // 加载状态
    isLoading: false,
    loadingText: '处理中...',

    // 历史数据模态框
    showHistoryModal: false,
    historyDataList: [],
    selectedHistoryIndex: null,

    // 清空所有数据模态框
    showClearAllModal: false,
    confirmText: '',
    canConfirmClearAll: false,

    // 防重复点击标志
    isProcessing: false,

    // 页面加载标志
    isPageLoaded: false
  },

  /**
   * 页面加载时
   */
  onLoad() {
    console.log('数据管理页面加载')

    // 获取全局管理器
    this.dataManager = getApp().getDataManager()
    this.userManager = getApp().getUserManager()
    this.syncManager = getApp().getSyncManager()

    // 检查用户登录状态
    if (!this.userManager.isUserLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    // 加载数据统计
    this.loadDataStats()
    this.setData({ isPageLoaded: true })
  },

  /**
   * 页面显示时
   */
  onShow() {
    // 只有在页面已经加载过的情况下才刷新数据统计
    if (this.data.isPageLoaded) {
      this.loadDataStats()
    }
  },

  /**
   * 页面隐藏时
   */
  onHide() {
    // 关闭所有模态框
    this.setData({
      showHistoryModal: false,
      showClearAllModal: false,
      isLoading: false
    })
  },

  /**
   * 页面卸载时
   */
  onUnload() {
    // 清理资源
    this.setData({
      showHistoryModal: false,
      showClearAllModal: false,
      isLoading: false
    })
  },

  /**
   * 加载数据统计
   */
  async loadDataStats() {
    try {
      // 加载本地数据统计
      this.loadLocalStats()

      // 加载云端数据统计
      await this.loadCloudStats()

      // 更新上传按钮状态
      this.updateUploadButtonState()

    } catch (error) {
      console.error('加载数据统计失败:', error)
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载本地数据统计
   */
  loadLocalStats() {
    try {
      const userData = this.dataManager.getUserData()

      if (!userData) {
        console.log('本地无数据')
        return
      }

      // 使用统一的统计函数
      const stats = calculateUserDataStats(userData)

      // 格式化最后修改时间
      const lastModified = userData.lastModified
      const lastModifiedText = lastModified ?
        formatDateTime(new Date(lastModified)) : '未知'

      this.setData({
        'localStats.workHistoryCount': stats.workHistoryCount,
        'localStats.timeSegmentCount': stats.timeSegmentCount,
        'localStats.fishingRecordCount': stats.fishingRecordCount,
        'localStats.incomeAdjustmentCount': stats.incomeAdjustmentCount,
        'localStats.lastModified': lastModified,
        'localStats.lastModifiedText': lastModifiedText
      })

      console.log('本地数据统计:', {
        ...stats,
        lastModifiedText
      })

    } catch (error) {
      console.error('加载本地数据统计失败:', error)
    }
  },

  /**
   * 加载云端数据统计
   */
  async loadCloudStats() {
    try {
      // 尝试调用云端数据统计API
      let result
      try {
        result = await api.sync.getCloudDataStats()
      } catch (statsError) {
        console.log('getCloudDataStats API暂未实现，使用getCloudDataInfo')
        // 如果统计API未实现，使用现有的getCloudDataInfo API
        result = await api.sync.getCloudDataInfo()
      }

      if (result && result.success && result.data) {
        const cloudData = result.data

        // 如果有详细统计数据，使用详细数据
        if (cloudData.stats) {
          this.setData({
            'cloudStats.workHistoryCount': cloudData.stats.workHistoryCount || 0,
            'cloudStats.timeSegmentCount': cloudData.stats.timeSegmentCount || 0,
            'cloudStats.fishingRecordCount': cloudData.stats.fishingRecordCount || 0,
            'cloudStats.incomeAdjustmentCount': cloudData.stats.incomeAdjustmentCount || 0
          })
        }

        // 格式化云端最后修改时间
        const lastModified = cloudData.lastModified
        const lastModifiedText = lastModified ?
          formatDateTime(new Date(lastModified)) : '未知'

        this.setData({
          'cloudStats.lastModified': lastModified,
          'cloudStats.lastModifiedText': lastModifiedText
        })

        console.log('云端数据信息:', {
          hasData: cloudData.hasData,
          lastModifiedText,
          stats: cloudData.stats
        })
      } else {
        console.log('获取云端数据信息失败或无数据')
        this.setData({
          'cloudStats.lastModifiedText': '无数据'
        })
      }

    } catch (error) {
      console.error('加载云端数据统计失败:', error)
      this.setData({
        'cloudStats.lastModifiedText': '获取失败'
      })
    }
  },

  /**
   * 更新上传按钮状态
   */
  updateUploadButtonState() {
    const localTime = this.data.localStats.lastModified
    const cloudTime = this.data.cloudStats.lastModified

    let canUpload = true
    let uploadButtonText = '上传到云端'

    if (!localTime) {
      canUpload = false
      uploadButtonText = '本地无数据'
    } else if (!cloudTime) {
      canUpload = true
      uploadButtonText = '上传到云端'
    } else {
      // 比较时间戳（转换为毫秒进行比较）
      const localTimestamp = new Date(localTime).getTime()
      const cloudTimestamp = new Date(cloudTime).getTime()

      if (Math.abs(localTimestamp - cloudTimestamp) < 1000) {
        // 时间差小于1秒，认为是相同的
        canUpload = false
        uploadButtonText = '数据已同步'
      } else if (localTimestamp > cloudTimestamp) {
        canUpload = true
        uploadButtonText = '上传到云端'
      } else {
        canUpload = true
        uploadButtonText = '强制上传'
      }
    }

    this.setData({
      canUpload,
      uploadButtonText
    })
  },

  /**
   * 上传数据到云端
   */
  async onUploadData() {
    if (!this.data.canUpload) {
      wx.showToast({
        title: this.data.uploadButtonText,
        icon: 'none'
      })
      return
    }

    if (this.data.isProcessing) {
      wx.showToast({
        title: '操作进行中，请稍候',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({ isProcessing: true })
      this.setData({
        isLoading: true,
        loadingText: '正在上传数据到云端...'
      })

      // 检查用户登录状态
      if (!this.userManager.isUserLoggedIn()) {
        throw new Error('用户未登录')
      }

      // 使用同步管理器上传数据
      await this.syncManager.uploadData()

      // 等待一下让上传完成
      await new Promise(resolve => setTimeout(resolve, 500))

      // 刷新数据统计
      await this.loadDataStats()

      wx.showToast({
        title: '数据上传成功',
        icon: 'success'
      })

    } catch (error) {
      console.error('上传数据失败:', error)

      let errorMessage = '上传失败'
      if (error.message.includes('未登录')) {
        errorMessage = '请先登录'
      } else if (error.message.includes('网络')) {
        errorMessage = '网络连接失败'
      } else if (error.message.includes('权限')) {
        errorMessage = '权限不足'
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      })
    } finally {
      this.setData({
        isLoading: false,
        isProcessing: false
      })
    }
  },

  /**
   * 加载云端历史数据
   */
  async onLoadHistoryData() {
    try {
      this.setData({
        isLoading: true,
        loadingText: '获取历史数据列表...'
      })

      // 检查用户登录状态
      if (!this.userManager.isUserLoggedIn()) {
        throw new Error('用户未登录')
      }

      let historyDataList = []

      try {
        // 尝试调用获取历史数据列表API
        const result = await api.sync.getHistoryDataList({ days: 7 })

        if (result && result.success && result.data && result.data.length > 0) {
          // 处理真实的历史数据
          historyDataList = result.data.map(item => ({
            timestamp: item.timestamp || item.lastModified,
            dateText: formatHistoryDate(new Date(item.timestamp || item.lastModified)),
            timeText: formatHistoryTime(new Date(item.timestamp || item.lastModified)),
            statsText: formatStatsText(item.stats),
            rawData: item
          }))
        } else {
          throw new Error('无历史数据')
        }
      } catch (apiError) {
        console.log('历史数据API暂未实现，使用模拟数据')

        // 生成模拟的历史数据
        historyDataList = this.generateMockHistoryData()
      }

      if (historyDataList.length === 0) {
        wx.showToast({
          title: '暂无历史数据',
          icon: 'none'
        })
        return
      }

      this.setData({
        historyDataList,
        showHistoryModal: true,
        selectedHistoryIndex: null
      })

    } catch (error) {
      console.error('获取历史数据失败:', error)

      let errorMessage = '获取历史数据失败'
      if (error.message.includes('未登录')) {
        errorMessage = '请先登录'
      } else if (error.message.includes('网络')) {
        errorMessage = '网络连接失败'
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none'
      })
    } finally {
      this.setData({
        isLoading: false
      })
    }
  },

  /**
   * 生成模拟历史数据
   */
  generateMockHistoryData() {
    const mockData = []
    const now = Date.now()

    for (let i = 1; i <= 7; i++) {
      const timestamp = now - i * 24 * 60 * 60 * 1000
      const date = new Date(timestamp)

      const stats = {
        workHistoryCount: Math.floor(Math.random() * 3) + 1,
        timeSegmentCount: Math.floor(Math.random() * 10) + 5,
        fishingRecordCount: Math.floor(Math.random() * 5),
        incomeAdjustmentCount: Math.floor(Math.random() * 3)
      }

      mockData.push({
        timestamp,
        dateText: formatHistoryDate(date),
        timeText: formatHistoryTime(date),
        statsText: formatStatsText(stats),
        rawData: {
          timestamp,
          stats: stats
        }
      })
    }

    return mockData
  },

  /**
   * 选择历史数据
   */
  onSelectHistoryData(e) {
    const index = e.currentTarget.dataset.index
    const historyDataList = this.data.historyDataList.map((item, i) => ({
      ...item,
      selected: i === index
    }))

    this.setData({
      historyDataList,
      selectedHistoryIndex: index
    })
  },

  /**
   * 确认加载历史数据
   */
  async onConfirmLoadHistory() {
    if (this.data.selectedHistoryIndex === null) {
      wx.showToast({
        title: '请选择历史数据',
        icon: 'none'
      })
      return
    }

    const selectedData = this.data.historyDataList[this.data.selectedHistoryIndex]

    // 先关闭历史数据模态框
    this.setData({
      showHistoryModal: false
    })

    try {
      // 检查时间冲突
      const conflictWarning = await this.checkHistoryDataConflict(selectedData)

      if (conflictWarning) {
        // 显示冲突警告，让用户确认
        wx.showModal({
          title: '⚠️ 时间冲突警告',
          content: conflictWarning,
          confirmText: '仍要加载',
          cancelText: '取消',
          success: async (res) => {
            if (res.confirm) {
              await this.performHistoryDataLoad(selectedData)
            }
          }
        })
      } else {
        // 没有冲突，直接加载
        await this.performHistoryDataLoad(selectedData)
      }

    } catch (error) {
      console.error('加载历史数据失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 检查历史数据时间冲突
   */
  async checkHistoryDataConflict(selectedData) {
    try {
      // 获取当前云端数据信息
      const cloudInfo = await api.sync.getCloudDataInfo()

      if (!cloudInfo || !cloudInfo.success || !cloudInfo.data) {
        return null // 无云端数据，无冲突
      }

      const cloudTimestamp = new Date(cloudInfo.data.lastModified).getTime()
      const historyTimestamp = new Date(selectedData.timestamp).getTime()

      // 如果历史数据时间早于云端最新数据时间，可能会覆盖云端数据
      if (historyTimestamp < cloudTimestamp) {
        const cloudTimeText = formatDateTime(new Date(cloudTimestamp))
        const historyTimeText = formatDateTime(new Date(historyTimestamp))

        return `您选择的历史数据时间为 ${historyTimeText}，但云端最新数据时间为 ${cloudTimeText}。\n\n加载历史数据后，本地时间将更新为当前时间，这可能会覆盖云端的最新数据。\n\n请谨慎操作，确认要继续吗？`
      }

      return null // 无冲突

    } catch (error) {
      console.error('检查时间冲突失败:', error)
      return '无法检查时间冲突，加载历史数据可能会覆盖云端最新数据，请谨慎操作。'
    }
  },

  /**
   * 执行历史数据加载
   */
  async performHistoryDataLoad(selectedData) {
    try {
      this.setData({
        isLoading: true,
        loadingText: '正在下载历史数据...'
      })

      let historyData

      try {
        // 尝试下载真实的历史数据
        const result = await api.sync.getHistoryData({
          timestamp: selectedData.timestamp
        })

        if (result && result.success && result.data) {
          historyData = result.data
        } else {
          throw new Error('下载历史数据失败')
        }
      } catch (apiError) {
        console.log('历史数据API暂未实现，使用模拟数据')

        // 使用模拟数据
        historyData = this.generateMockUserData(selectedData)
      }

      this.setData({
        loadingText: '正在应用历史数据...'
      })

      // 暂停数据变化监听器，避免触发上传
      if (this.syncManager.pauseDataChangeListener !== undefined) {
        this.syncManager.pauseDataChangeListener = true
      }

      // 应用历史数据到本地
      this.dataManager.loadUserData(historyData, false)

      // 修改本地的lastModified为当前时间
      const userData = this.dataManager.getUserData()
      if (userData) {
        userData.lastModified = new Date()
        this.dataManager.saveData()
      }

      this.setData({
        loadingText: '准备重启小程序...'
      })

      // 延迟一下，让用户看到进度
      await new Promise(resolve => setTimeout(resolve, 1000))

      wx.showToast({
        title: '历史数据加载成功',
        icon: 'success'
      })

      // 重启小程序
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        })
      }, 1500)

    } catch (error) {
      console.error('执行历史数据加载失败:', error)
      wx.showToast({
        title: '加载历史数据失败',
        icon: 'none'
      })
      this.setData({
        isLoading: false
      })
    }
  },

  /**
   * 生成模拟用户数据
   */
  generateMockUserData(selectedData) {
    const now = new Date()
    return {
      version: '0.3.0',
      lastModified: new Date(selectedData.timestamp),
      createTime: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // 30天前
      workHistory: {
        'work_1': {
          id: 'work_1',
          name: '模拟工作',
          hourlyRate: 50,
          timeTracking: {}
        }
      },
      settings: {
        currentWorkId: 'work_1',
        income: {
          currencySymbol: '¥',
          currencyCode: 'CNY'
        }
      }
    }
  },

  /**
   * 清空缓存
   */
  async onClearCache() {
    try {
      // 获取所有存储键信息
      const storageInfo = wx.getStorageInfoSync()
      const allKeys = storageInfo.keys

      // 保留的用户数据键
      const preserveKeys = ['USER_DATA']

      // 将要清除的缓存键
      const cacheKeys = allKeys.filter(key => !preserveKeys.includes(key))

      // 构建提示信息
      const cacheInfo = `此操作将清除应用缓存和临时数据，但不会影响您的工作数据。\n\n共 ${cacheKeys.length} 项缓存数据将被清除，是否继续？`

      wx.showModal({
        title: '确认清空缓存',
        content: cacheInfo,
        confirmText: '清空',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            this.setData({
              isLoading: true,
              loadingText: '正在清空缓存...'
            })

            let clearedCount = 0
            let failedCount = 0

            // 清除缓存键
            cacheKeys.forEach(key => {
              try {
                wx.removeStorageSync(key)
                console.log('已清除缓存键:', key)
                clearedCount++
              } catch (error) {
                console.error('清除缓存键失败:', key, error)
                failedCount++
              }
            })

            this.setData({
              isLoading: false
            })

            // 显示结果
            if (failedCount === 0) {
              wx.showToast({
                title: `缓存清空成功 (${clearedCount}项)`,
                icon: 'success',
                duration: 2000
              })
            } else {
              wx.showToast({
                title: `部分清空成功 (${clearedCount}/${clearedCount + failedCount})`,
                icon: 'none',
                duration: 2000
              })
            }

            // 清空缓存后，重新加载数据统计
            setTimeout(() => {
              this.loadDataStats()
            }, 1000)
          }
        }
      })

    } catch (error) {
      console.error('清空缓存失败:', error)
      wx.showToast({
        title: '清空缓存失败',
        icon: 'none'
      })
      this.setData({
        isLoading: false
      })
    }
  },

  /**
   * 清空所有数据
   */
  onClearAllData() {
    this.setData({
      showClearAllModal: true,
      confirmText: '',
      canConfirmClearAll: false
    })
  },

  /**
   * 确认文本输入
   */
  onConfirmTextInput(e) {
    const confirmText = e.detail.value
    const canConfirmClearAll = confirmText === '确认清空云端和本地数据'

    this.setData({
      confirmText,
      canConfirmClearAll
    })
  },

  /**
   * 确认清空所有数据
   */
  async onConfirmClearAll() {
    if (!this.data.canConfirmClearAll) {
      wx.showToast({
        title: '请输入确认文字',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({
        showClearAllModal: false,
        isLoading: true,
        loadingText: '正在清空云端数据...'
      })

      // 检查用户登录状态
      if (!this.userManager.isUserLoggedIn()) {
        throw new Error('用户未登录，无法清空云端数据')
      }

      let cloudClearSuccess = false

      try {
        // 1. 清空云端数据
        const result = await api.sync.clearCloudData()

        if (result && result.success) {
          cloudClearSuccess = true
          console.log('云端数据清空成功')
        } else {
          throw new Error(result?.message || '清空云端数据失败')
        }
      } catch (cloudError) {
        console.error('清空云端数据失败:', cloudError)

        // 询问用户是否继续清空本地数据
        const continueLocal = await new Promise((resolve) => {
          wx.showModal({
            title: '云端数据清空失败',
            content: '清空云端数据失败，是否仍要清空本地数据？',
            confirmText: '继续',
            cancelText: '取消',
            success: (res) => resolve(res.confirm)
          })
        })

        if (!continueLocal) {
          this.setData({ isLoading: false })
          return
        }
      }

      this.setData({
        loadingText: '正在清空本地数据...'
      })

      // 2. 清空本地数据
      this.dataManager.clearAllData()

      // 3. 清空所有localStorage
      try {
        const storageInfo = wx.getStorageInfoSync()
        storageInfo.keys.forEach(key => {
          try {
            wx.removeStorageSync(key)
          } catch (error) {
            console.error('清除存储键失败:', key, error)
          }
        })
      } catch (error) {
        console.error('清空localStorage失败:', error)
      }

      this.setData({
        loadingText: '准备重启小程序...'
      })

      // 显示成功信息
      const successMessage = cloudClearSuccess ?
        '云端和本地数据已全部清空' :
        '本地数据已清空，云端数据清空失败'

      wx.showToast({
        title: successMessage,
        icon: cloudClearSuccess ? 'success' : 'none',
        duration: 2000
      })

      // 4. 重启小程序
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        })
      }, 2000)

    } catch (error) {
      console.error('清空所有数据失败:', error)

      let errorMessage = '清空数据失败'
      if (error.message.includes('未登录')) {
        errorMessage = '用户未登录'
      } else if (error.message.includes('网络')) {
        errorMessage = '网络连接失败'
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      })

      this.setData({
        isLoading: false
      })
    }
  },

  /**
   * 关闭历史数据模态框
   */
  onCloseHistoryModal() {
    this.setData({
      showHistoryModal: false,
      selectedHistoryIndex: null
    })
  },

  /**
   * 关闭清空所有数据模态框
   */
  onCloseClearAllModal() {
    this.setData({
      showClearAllModal: false,
      confirmText: '',
      canConfirmClearAll: false
    })
  },

  /**
   * 跳转到反馈页面
   */
  onGoToFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/index'
    })
  },

  /**
   * 模态框内容点击（阻止冒泡）
   */
  onModalContentTap() {
    // 阻止事件冒泡，防止关闭模态框
  },

})
